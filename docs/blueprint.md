# **App Name**: BaliBlissed Journeys

## Core Features:

- Destination Showcase: Display a landing page with featured Bali destinations and travel packages.
- Advanced Search: Implement a user-friendly search functionality to filter travel options based on interests and dates.
- Detailed Destination Pages: Enable users to browse detailed information, photo galleries, and interactive maps for each Bali destination.
- Package Offers: Offer curated travel packages with accommodation, tours, and activities tailored to different preferences.
- AI Travel Assistant: AI-powered chatbot that suggests destinations, itineraries, and answers user queries related to Bali travel, and uses tool based decisionmaking to add specific details.
- Contact Form: Allow users to submit inquiries.

## Style Guidelines:

- Primary color: Deep turquoise (#40E0D0) evoking the vibrant ocean and lush landscapes of Bali.
- Background color: Light beige (#F5F5DC), providing a clean, calming backdrop.
- Accent color: Warm coral (#FF7F50), highlighting key interactions and calls to action.
- Body font: 'PT Sans' (sans-serif) for clear, readable content; also suitable for headlines.
- Use detailed icons representing Bali's attractions (temples, beaches, dance) with a touch of Balinese art style.
- Employ a grid-based layout with visually appealing destination photography.
- Subtle transitions and parallax scrolling effects to enhance user engagement.
